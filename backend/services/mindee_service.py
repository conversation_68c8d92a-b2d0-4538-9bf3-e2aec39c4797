import os
import requests
import json
import logging
import traceback
from flask import current_app

# Configure logger
logger = logging.getLogger('ocr')

class MindeeService:
    """
    Service for interacting with Mindee API for invoice processing.
    """

    def __init__(self, api_key=None):
        """
        Initialize the Mindee service with API key.
        """
        self.api_key = api_key or os.environ.get('MINDEE_API_KEY')

        # Default generic invoice API
        self.base_url = "https://api.mindee.net/v1/products/mindee/invoices/v4/predict"

        # Custom vendor-specific APIs
        self.vendor_apis = {
            'bova': "https://api.mindee.net/v1/products/avmaman7/bova/v1/predict_async",
            'kast': "https://api.mindee.net/v1/products/avmaman7/kast/v1/predict_async",
            'dekalb': "https://api.mindee.net/v1/products/avmaman7/dekalb_produce/v1/predict_async",
        }

        # Define document URLs for different vendors (used for retrieving document details)
        self.document_urls = {
            'bova': "https://api.mindee.net/v1/products/avmaman7/bova/v1/documents",
            'kast': "https://api.mindee.net/v1/products/avmaman7/kast/v1/documents",
            'dekalb': "https://api.mindee.net/v1/products/avmaman7/dekalb_produce/v1/documents",
            'generic': "https://api.mindee.net/v1/products/mindee/invoices/v4/documents"
        }

        if not self.api_key:
            logger.warning("Mindee API key not found. Using mock data for development.")

    def process_invoice(self, image_path, vendor='generic'):
        """
        Process an invoice image using Mindee API.

        Args:
            image_path: Path to the invoice image file
            vendor: Vendor type (bova, kast, dekalb, or generic)

        Returns:
            dict: Structured data extracted from the invoice
        """
        if not self.api_key:
            logger.warning("No Mindee API key available. Using mock data.")
            return self._mock_response()

        try:
            # Use generic OCR for Dekalb (custom API not working well)
            # Bova and Kast custom APIs have been fixed and can be used
            if vendor == 'dekalb':
                logger.info(f"Using generic OCR instead of {vendor} custom API (custom API not reliable)")
                vendor = 'generic'

            # Select the appropriate API URL based on vendor
            api_url = self.vendor_apis.get(vendor, self.base_url)
            is_async = '_async' in api_url

            logger.info(f"Processing invoice for vendor: {vendor} using API: {api_url}")

            # Check if image_path is a string (file path) or a file-like object
            if isinstance(image_path, str):
                # It's a file path, open the file
                if not os.path.exists(image_path):
                    logger.error(f"File not found: {image_path}")
                    return {
                        "success": False,
                        "error": f"File not found: {image_path}",
                        "items": []
                    }

                with open(image_path, 'rb') as file:
                    file_content = file.read()
            else:
                # It's a file-like object, read its content
                try:
                    # Try to get the file content
                    if hasattr(image_path, 'read'):
                        # Reset file pointer to beginning if possible
                        if hasattr(image_path, 'seek'):
                            image_path.seek(0)
                        file_content = image_path.read()
                    else:
                        logger.error("Invalid file object: does not have 'read' method")
                        return {
                            "success": False,
                            "error": "Invalid file object",
                            "items": []
                        }
                except Exception as file_error:
                    logger.error(f"Error reading file: {str(file_error)}")
                    return {
                        "success": False,
                        "error": f"Error reading file: {str(file_error)}",
                        "items": []
                    }

            # Create the files dictionary with the file content
            # The key issue is here - Mindee expects the file to be sent with the key 'document'
            # and the file object itself, not a tuple with filename and content
            logger.info(f"Sending request to Mindee API for file: {image_path if isinstance(image_path, str) else 'file object'}")

            if isinstance(image_path, str):
                # If it's a file path, open the file directly in the request
                with open(image_path, 'rb') as file:
                    files = {'document': file}
                    headers = {'Authorization': f'Token {self.api_key}'}

                    response = requests.post(
                        api_url,
                        files=files,
                        headers=headers
                    )
            else:
                # For file-like objects, use the content we already read
                files = {'document': ('invoice.jpg', file_content, 'image/jpeg')}
                headers = {'Authorization': f'Token {self.api_key}'}

                response = requests.post(
                    api_url,
                    files=files,
                    headers=headers
                )

            if response.status_code in [200, 201, 202]:
                result = response.json()
                logger.info(f"Successfully processed invoice with Mindee API (status: {response.status_code})")
                # Log the full response for debugging
                logger.debug(f"Full Mindee API response: {json.dumps(result, indent=2)}")

                # Handle async API responses
                if is_async and 'job' in result:
                    job_id = result.get('job', {}).get('id')
                    polling_url = result.get('job', {}).get('polling_url')
                    if job_id and polling_url:
                        # Wait for the job to complete and get results
                        return self._get_async_result(job_id, polling_url, vendor)

                # Parse based on vendor
                if vendor == 'bova':
                    return self._parse_bova_response(result)
                elif vendor == 'kast':
                    return self._parse_kast_response(result)
                elif vendor == 'dekalb':
                    return self._parse_dekalb_response(result)
                else:
                    return self._parse_mindee_response(result)
            else:
                logger.error(f"Mindee API error: {response.status_code} - {response.text}")
                return {
                    "success": False,
                    "error": f"API error: {response.status_code}",
                    "items": []
                }

        except Exception as e:
            logger.error(f"Error processing invoice with Mindee: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "items": []
            }

    def _parse_mindee_response(self, response):
        """
        Parse the response from Mindee API into our application's format.

        Args:
            response: JSON response from Mindee API

        Returns:
            dict: Structured data in our application's format
        """
        try:
            document = response.get('document', {})
            inference = document.get('inference', {})
            prediction = inference.get('prediction', {})

            logger.debug(f"Parsing Mindee response: {json.dumps(prediction, indent=2)}")

            # Extract invoice details
            invoice_number = self._get_field_value(prediction, 'invoice_number')
            date = self._get_field_value(prediction, 'date')
            due_date = self._get_field_value(prediction, 'due_date')
            total_amount = self._get_field_value(prediction, 'total_amount')

            # Extract supplier info
            supplier_name = self._get_field_value(prediction, 'supplier_name')
            supplier_address = self._get_field_value(prediction, 'supplier_address')

            # Extract line items
            line_items = []
            if 'line_items' in prediction:
                for item in prediction['line_items']:
                    # Handle different response formats
                    description = ''
                    quantity = 0
                    unit_price = 0
                    amount = 0

                    # Get description
                    if isinstance(item.get('description'), dict):
                        description = item.get('description', {}).get('value', '')
                    else:
                        description = item.get('description', '')

                    # Get quantity
                    if isinstance(item.get('quantity'), dict):
                        quantity_val = item.get('quantity', {}).get('value', 0)
                    else:
                        quantity_val = item.get('quantity', 0)

                    # Get unit price
                    if isinstance(item.get('unit_price'), dict):
                        unit_price_val = item.get('unit_price', {}).get('value', 0)
                    else:
                        unit_price_val = item.get('unit_price', 0)

                    # Get total amount
                    if isinstance(item.get('total_amount'), dict):
                        amount_val = item.get('total_amount', {}).get('value', 0)
                    else:
                        amount_val = item.get('total_amount', 0)

                    # Convert to float, handling None values
                    try:
                        quantity = float(quantity_val) if quantity_val is not None else 0
                    except (ValueError, TypeError):
                        quantity = 0

                    try:
                        unit_price = float(unit_price_val) if unit_price_val is not None else 0
                    except (ValueError, TypeError):
                        unit_price = 0

                    try:
                        amount = float(amount_val) if amount_val is not None else 0
                    except (ValueError, TypeError):
                        amount = 0

                    # Check if we need to split this into multiple line items
                    if description and len(description) > 100:
                        # This might be multiple items combined
                        # Try to split by common separators
                        parts = []
                        for sep in [' Produce ', ' Chicken ', ' Box ', ' Sauce ', ' Spice ']:
                            if sep in description and description.count(sep) > 1:
                                parts = description.split(sep)
                                # Add the separator back to each part except the first
                                for i in range(1, len(parts)):
                                    parts[i] = sep.strip() + ' ' + parts[i]
                                break

                        # If we split the description, create separate line items
                        if parts and len(parts) > 1:
                            # Distribute the amount among parts if possible
                            part_amount = amount / len(parts) if amount > 0 else 0
                            part_quantity = quantity / len(parts) if quantity > 0 else 1

                            for part in parts:
                                if part.strip():
                                    line_item = {
                                        'description': part.strip(),
                                        'quantity': part_quantity,
                                        'unit_price': unit_price,
                                        'amount': part_amount
                                    }
                                    line_items.append(line_item)
                        else:
                            # No splitting needed or splitting failed
                            line_item = {
                                'description': description,
                                'quantity': quantity,
                                'unit_price': unit_price,
                                'amount': amount
                            }
                            line_items.append(line_item)
                    else:
                        # Regular line item
                        line_item = {
                            'description': description,
                            'quantity': quantity,
                            'unit_price': unit_price,
                            'amount': amount
                        }
                        line_items.append(line_item)

            # If total_amount is a dictionary, extract the value
            if isinstance(total_amount, dict):
                total_amount = total_amount.get('value', 0)

            # Convert total_amount to float
            try:
                total_amount = float(total_amount) if total_amount is not None else 0
            except (ValueError, TypeError):
                total_amount = 0

            return {
                "success": True,
                "invoice_number": invoice_number,
                "date": date,
                "due_date": due_date,
                "total_amount": total_amount,
                "supplier_name": supplier_name,
                "supplier_address": supplier_address,
                "items": line_items
            }

        except Exception as e:
            logger.error(f"Error parsing Mindee response: {str(e)}")
            return {
                "success": False,
                "error": f"Error parsing response: {str(e)}",
                "items": []
            }

    def _get_field_value(self, prediction, field_name):
        """
        Helper method to extract field values from Mindee prediction.

        Args:
            prediction: Mindee prediction object
            field_name: Name of the field to extract

        Returns:
            The value of the field or empty string if not found
        """
        if field_name in prediction:
            field = prediction[field_name]
            # Handle different response formats
            if isinstance(field, dict):
                return field.get('value', '')
            return field
        return ''

    def _get_async_result(self, job_id, polling_url, vendor):
        """
        Get results from an asynchronous Mindee API job.

        Args:
            job_id: The job ID returned by the async API
            polling_url: The URL to poll for job status
            vendor: The vendor type

        Returns:
            dict: Structured data extracted from the invoice
        """
        max_attempts = 30  # Increase max attempts for longer-running jobs
        wait_time = 2  # seconds

        logger.info(f"Starting to poll for async job results: {job_id} for vendor: {vendor}")
        logger.info(f"Using polling URL: {polling_url}")

        for attempt in range(max_attempts):
            try:
                # Use the polling URL provided in the response
                headers = {'Authorization': f'Token {self.api_key}'}

                logger.info(f"Checking async job status (attempt {attempt+1}/{max_attempts}): {job_id}")
                response = requests.get(polling_url, headers=headers)

                if response.status_code == 200:
                    job_result = response.json()
                    status = job_result.get('job', {}).get('status')

                    if status == 'completed':
                        logger.info(f"Async job completed: {job_id}")

                        # For vendor-specific APIs, we need to get the document from the job result
                        if 'document_id' in job_result.get('job', {}):
                            document_id = job_result.get('job', {}).get('document_id')
                            logger.info(f"Getting document details for document ID: {document_id}")

                            # Construct the URL to get document details
                            vendor_key = vendor if vendor in self.document_urls else 'generic'
                            doc_url = f"{self.document_urls[vendor_key]}/{document_id}"
                            logger.info(f"Using document URL for vendor {vendor}: {doc_url}")

                            # Get document details
                            doc_response = requests.get(doc_url, headers=headers)

                            if doc_response.status_code == 200:
                                doc_result = doc_response.json()
                                logger.info(f"Successfully retrieved document details for ID: {document_id}")

                                # Parse based on vendor
                                if vendor == 'bova':
                                    return self._parse_bova_response(doc_result)
                                elif vendor == 'kast':
                                    return self._parse_kast_response(doc_result)
                                elif vendor == 'dekalb':
                                    return self._parse_dekalb_response(doc_result)
                                else:
                                    return self._parse_mindee_response(doc_result)
                            else:
                                logger.error(f"Error getting document details: {doc_response.status_code} - {doc_response.text}")
                                return {
                                    "success": False,
                                    "error": f"Error getting document details: {doc_response.status_code}",
                                    "items": []
                                }
                        else:
                            # If no document_id, use the job result directly
                            # Parse based on vendor
                            if vendor == 'bova':
                                return self._parse_bova_response(job_result)
                            elif vendor == 'kast':
                                return self._parse_kast_response(job_result)
                            elif vendor == 'dekalb':
                                return self._parse_dekalb_response(job_result)
                            else:
                                return self._parse_mindee_response(job_result)

                    elif status == 'error':
                        error_msg = job_result.get('job', {}).get('error', 'Unknown error')
                        logger.error(f"Async job failed: {error_msg}")
                        return {
                            "success": False,
                            "error": f"Async job failed: {error_msg}",
                            "items": []
                        }

                    # If still processing, wait and try again
                    logger.info(f"Job still processing (status: {status}), waiting {wait_time} seconds...")
                    import time
                    time.sleep(wait_time)
                    # Increase wait time for next attempt
                    wait_time = min(wait_time * 1.5, 10)  # Cap at 10 seconds

                else:
                    logger.error(f"Error checking job status: {response.status_code} - {response.text}")
                    return {
                        "success": False,
                        "error": f"Error checking job status: {response.status_code}",
                        "items": []
                    }

            except Exception as e:
                logger.error(f"Error checking async job: {str(e)}")
                return {
                    "success": False,
                    "error": f"Error checking async job: {str(e)}",
                    "items": []
                }

        # If we've exhausted all attempts
        return {
            "success": False,
            "error": "Timed out waiting for async job to complete",
            "items": []
        }

    def _parse_bova_response(self, response):
        """
        Parse response from Bova custom API.

        Args:
            response: JSON response from Mindee API

        Returns:
            dict: Structured data in our application's format
        """
        try:
            logger.info("=== BOVA RESPONSE PARSER CALLED ===")
            logger.info(f"Response type: {type(response)}")
            logger.info(f"Response keys: {list(response.keys()) if isinstance(response, dict) else 'Not a dict'}")
            # Log the full response for debugging
            logger.info(f"Received Bova response: {json.dumps(response, indent=2)[:1000]}...")
            # Also log the complete response to a separate log level for debugging
            logger.debug(f"COMPLETE Bova response: {json.dumps(response, indent=2)}")

            # TEMPORARY: Log all available fields for debugging
            if 'document' in response and 'inference' in response['document'] and 'prediction' in response['document']['inference']:
                prediction = response['document']['inference']['prediction']
                logger.info(f"BOVA DEBUG - Available fields: {list(prediction.keys())}")
                for field_name, field_value in prediction.items():
                    if field_name not in ['line_items']:  # Don't log line items as they're verbose
                        logger.info(f"BOVA DEBUG - {field_name}: {field_value}")

            # Debug: Save response to file for debugging (can be removed later)
            # import tempfile
            # with tempfile.NamedTemporaryFile(mode='w', suffix='_bova_response.json', delete=False) as f:
            #     json.dump(response, f, indent=2)
            #     logger.info(f"Saved Bova response to: {f.name}")

            # Log the structure of the response to help with debugging
            def log_structure(obj, prefix=''):
                if isinstance(obj, dict):
                    for k, v in obj.items():
                        logger.debug(f"{prefix}Key: {k} (Type: {type(v).__name__})")
                        if isinstance(v, (dict, list)) and k != 'pages':  # Skip pages to avoid too much output
                            log_structure(v, prefix + '  ')
                elif isinstance(obj, list) and len(obj) > 0:
                    logger.debug(f"{prefix}List with {len(obj)} items, first item type: {type(obj[0]).__name__}")
                    if len(obj) > 0 and isinstance(obj[0], (dict, list)):
                        log_structure(obj[0], prefix + '  ')

            logger.debug("Structure of Bova response:")
            log_structure(response)

            # Handle different response formats
            prediction = {}

            # Try multiple paths to find the prediction data
            # Path 1: Standard document response
            document = response.get('document', {})
            if 'inference' in document:
                inference = document.get('inference', {})
                prediction = inference.get('prediction', {})
                logger.info("Found prediction in standard document response")

            # Path 2: Job response with document
            elif 'api_request' in response and 'document' in response:
                doc = response.get('document', {})
                if 'inference' in doc:
                    inference = doc.get('inference', {})
                    prediction = inference.get('prediction', {})
                    logger.info("Found prediction in job response document")

            # Path 3: Job response with result
            elif 'job' in response and 'result' in response.get('job', {}):
                result = response.get('job', {}).get('result', {})
                if 'document' in result:
                    doc = result.get('document', {})
                    if 'inference' in doc:
                        inference = doc.get('inference', {})
                        prediction = inference.get('prediction', {})
                        logger.info("Found prediction in job result document")

            # Path 4: Direct prediction in response
            elif 'prediction' in response:
                prediction = response.get('prediction', {})
                logger.info("Found prediction directly in response")

            # Path 5: Look for fields directly in the response
            else:
                logger.info("Searching for prediction data directly in response")
                for key in ['invoice_number', 'date', 'due_date', 'total_amount', 'line_items']:
                    if key in response:
                        prediction[key] = response[key]
                if prediction:
                    logger.info("Found prediction fields directly in response")

            # If we still don't have prediction data, log an error
            if not prediction:
                logger.error("Could not find prediction data in any expected location in the response")
                # Log the keys at the top level to help debug
                logger.error(f"Top-level keys in response: {list(response.keys())}")
                if 'document' in response:
                    logger.error(f"Keys in document: {list(response['document'].keys())}")
                return {
                    "success": False,
                    "error": "Could not find prediction data in response",
                    "items": []
                }

            logger.debug(f"Parsing Bova prediction data: {json.dumps(prediction, indent=2)[:500]}...")

            # Log all available fields in prediction for debugging
            logger.info(f"Available fields in Bova prediction: {list(prediction.keys())}")

            # Log detailed field values for debugging
            for field_name, field_value in prediction.items():
                if field_name != 'line_items':  # Don't log line items as they're verbose
                    logger.info(f"BOVA FIELD DEBUG - {field_name}: {field_value}")

            # Extract invoice details - try multiple field names
            # Based on actual Mindee response: Invoice field contains the invoice number
            invoice_number = self._get_field_value(prediction, 'Invoice')  # Primary field for Bova
            if not invoice_number:
                invoice_number = self._get_field_value(prediction, 'invoice_number')
            if not invoice_number:
                # Try alternative field names
                alt_invoice_fields = ['invoice_id', 'document_number', 'reference_number', 'number']
                for field in alt_invoice_fields:
                    invoice_number = self._get_field_value(prediction, field)
                    if invoice_number:
                        logger.info(f"Found invoice number in field: {field}")
                        break

            # For Bova, based on actual Mindee response: Inv Date field contains the date
            date = self._get_field_value(prediction, 'Inv Date')  # Primary field for Bova
            if not date:
                date = self._get_field_value(prediction, 'Invoice Date')  # Try alternative
            if not date:
                date = self._get_field_value(prediction, 'date')
            if not date:
                # Try alternative field names
                alt_date_fields = ['invoice_date', 'document_date', 'issue_date', 'created_date', 'Date']
                for field in alt_date_fields:
                    date = self._get_field_value(prediction, field)
                    if date:
                        logger.info(f"Found date in field: {field}")
                        break

            due_date = self._get_field_value(prediction, 'due_date')

            # For Bova, based on actual Mindee response: Please Pay field contains the total
            total_amount = self._get_field_value(prediction, 'Please Pay')  # Primary field for Bova
            if not total_amount:
                total_amount = self._get_field_value(prediction, 'Total Amount')  # Try alternative
            if not total_amount:
                total_amount = self._get_field_value(prediction, 'Invoice Total')
            if not total_amount:
                total_amount = self._get_field_value(prediction, 'total_amount')
            if not total_amount:
                # Try alternative field names
                alt_total_fields = ['total', 'grand_total', 'amount_total', 'invoice_total', 'net_amount', 'Total', 'Extension']
                for field in alt_total_fields:
                    total_amount = self._get_field_value(prediction, field)
                    if total_amount:
                        logger.info(f"Found total amount in field: {field}")
                        break

            # Set supplier name to Bova since we know the vendor
            supplier_name = "Bova"
            supplier_address = self._get_field_value(prediction, 'supplier_address')

            # Log extracted fields
            logger.info(f"Extracted Bova invoice details: Number={invoice_number}, Date={date}, Total={total_amount}")

            # Extract line items
            line_items = []
            if 'line_items' in prediction:
                logger.info(f"Found {len(prediction['line_items'])} line items in Bova response")
                for i, item in enumerate(prediction['line_items']):
                    # Log available fields in first line item for debugging
                    if i == 0:
                        logger.info(f"BOVA LINE ITEM DEBUG - Available fields in first item: {list(item.keys())}")
                        for field_name, field_value in item.items():
                            logger.info(f"BOVA LINE ITEM DEBUG - {field_name}: {field_value}")

                    # Extract line item fields with improved description handling
                    # Updated field names based on new model structure
                    description = self._get_field_value(item, 'Description')  # Primary field for both Bova and Kast

                    # If description is empty, try multiple alternative fields including the typo
                    if not description:
                        description = self._get_field_value(item, 'description')  # Fallback to old names
                    if not description:
                        # Try alternative field names that might contain description
                        alt_fields = ['descripiton', 'product_description', 'item_description', 'name', 'product_name', 'item_name']
                        for field in alt_fields:
                            description = self._get_field_value(item, field)
                            if description:
                                break

                    # If still no description, try to use product_code or other identifiers
                    if not description:
                        product_code = self._get_field_value(item, 'product_code')
                        item_code = self._get_field_value(item, 'item_code')
                        sku = self._get_field_value(item, 'sku')

                        if product_code:
                            description = f"Product {product_code}"
                        elif item_code:
                            description = f"Item {item_code}"
                        elif sku:
                            description = f"SKU {sku}"
                        else:
                            # Log this case for debugging
                            logger.warning(f"Bova line item {len(line_items) + 1} has no description or identifiers. Item data: {item}")
                            # Skip items without any description rather than creating generic names
                            continue

                    # Try multiple field names for quantity (Bova-specific)
                    # Updated field names based on new model structure
                    quantity = self._get_field_value(item, 'Quantity Shipped')  # Primary field for Bova
                    if not quantity:
                        quantity = self._get_field_value(item, 'Quantity Ordered')
                        if quantity:
                            logger.debug(f"Bova: Found quantity in Quantity Ordered: {quantity}")
                    if not quantity:
                        quantity = self._get_field_value(item, 'quantity_shipped')  # Fallback to old names
                        if quantity:
                            logger.debug(f"Bova: Found quantity in quantity_shipped: {quantity}")
                    if not quantity:
                        quantity = self._get_field_value(item, 'quantity_ordered')
                        if quantity:
                            logger.debug(f"Bova: Found quantity in quantity_ordered: {quantity}")
                    if not quantity:
                        quantity = self._get_field_value(item, 'quantity')
                        if quantity:
                            logger.debug(f"Bova: Found quantity in quantity: {quantity}")

                    # Try multiple field names for unit_price (Bova-specific)
                    # Updated field names based on new model structure
                    unit_price = self._get_field_value(item, 'Selling Price')  # Primary field for Bova
                    if not unit_price:
                        unit_price = self._get_field_value(item, 'selling_price')  # Fallback to old names
                        if unit_price:
                            logger.debug(f"Bova: Found unit_price in selling_price: {unit_price}")
                    if not unit_price:
                        unit_price = self._get_field_value(item, 'unit_price')
                        if unit_price:
                            logger.debug(f"Bova: Found unit_price in unit_price: {unit_price}")
                    if not unit_price:
                        unit_price = self._get_field_value(item, 'price')
                        if unit_price:
                            logger.debug(f"Bova: Found unit_price in price: {unit_price}")

                    # Try multiple field names for amount
                    # Based on actual Mindee response: Extension field contains the line total
                    amount = self._get_field_value(item, 'Extension')  # Primary field for Bova
                    if not amount:
                        amount = self._get_field_value(item, 'extensions')  # Fallback to old names
                    if not amount:
                        amount = self._get_field_value(item, 'extended')
                    if not amount:
                        amount = self._get_field_value(item, 'amount')

                    # Log what we found for debugging
                    if amount:
                        logger.debug(f"Bova: Found amount in Extension field: {amount}")
                    else:
                        logger.debug(f"Bova: No amount found in any field for item: {description}")

                    # Convert amount to float first to check if it's valid
                    try:
                        amount_float = float(amount) if amount else 0
                    except (ValueError, TypeError):
                        amount_float = 0

                    # If still no quantity, default to 1 for line items that have amounts
                    if not quantity and amount_float > 0:
                        quantity = 1
                        logger.debug(f"Defaulting quantity to 1 for item with amount: {amount_float}")

                    # Log available fields in item for debugging
                    if not quantity:
                        logger.debug(f"No quantity found. Available fields in item: {list(item.keys())}")

                    # Convert to appropriate types
                    try:
                        quantity = float(quantity) if quantity else 0
                    except (ValueError, TypeError):
                        quantity = 0

                    try:
                        unit_price = float(unit_price) if unit_price else 0
                    except (ValueError, TypeError):
                        unit_price = 0

                    # Use the already calculated amount_float
                    amount = amount_float

                    line_item = {
                        'description': description,
                        'quantity': quantity,
                        'unit_price': unit_price,
                        'amount': amount
                    }
                    line_items.append(line_item)
                    logger.debug(f"Added line item: {description}, qty: {quantity}, price: {unit_price}, amount: {amount}")

            # Convert total_amount to float
            try:
                total_amount = float(total_amount) if total_amount else 0
            except (ValueError, TypeError):
                total_amount = 0

            # If no total amount found, calculate from line items
            if total_amount == 0 and line_items:
                calculated_total = sum(item.get('amount', 0) for item in line_items)
                if calculated_total > 0:
                    total_amount = calculated_total
                    logger.info(f"Calculated total from line items: {total_amount}")

            result = {
                "success": True,
                "invoice_number": invoice_number,
                "date": date,
                "due_date": due_date,
                "total_amount": total_amount,
                "supplier_name": supplier_name,
                "supplier_address": supplier_address,
                "items": line_items
            }

            logger.info(f"Successfully parsed Bova response with {len(line_items)} line items")
            return result

        except Exception as e:
            logger.error(f"Error parsing Bova response: {str(e)}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            return {
                "success": False,
                "error": f"Error parsing Bova response: {str(e)}",
                "items": []
            }

    def _parse_kast_response(self, response):
        """
        Parse response from Kast custom API.

        Args:
            response: JSON response from Mindee API

        Returns:
            dict: Structured data in our application's format
        """
        try:
            # Log the full response for debugging
            logger.info(f"Full Kast response structure: {json.dumps(response, indent=2)}")
            logger.info(f"Received Kast response: {json.dumps(response, indent=2)[:1000]}...")

            # TEMPORARY: Log all available fields for debugging
            if 'document' in response and 'inference' in response['document'] and 'prediction' in response['document']['inference']:
                prediction = response['document']['inference']['prediction']
                logger.info(f"KAST DEBUG - Available fields: {list(prediction.keys())}")
                for field_name, field_value in prediction.items():
                    if field_name not in ['line_items']:  # Don't log line items as they're verbose
                        logger.info(f"KAST DEBUG - {field_name}: {field_value}")

            # Debug: Save response to file for debugging (can be removed later)
            # import tempfile
            # with tempfile.NamedTemporaryFile(mode='w', suffix='_kast_response.json', delete=False) as f:
            #     json.dump(response, f, indent=2)
            #     logger.info(f"Saved Kast response to: {f.name}")

            # Log the structure of the response to help with debugging
            def log_structure(obj, prefix=''):
                if isinstance(obj, dict):
                    for k, v in obj.items():
                        logger.debug(f"{prefix}Key: {k} (Type: {type(v).__name__})")
                        if isinstance(v, (dict, list)) and k != 'pages':  # Skip pages to avoid too much output
                            log_structure(v, prefix + '  ')
                elif isinstance(obj, list) and len(obj) > 0:
                    logger.debug(f"{prefix}List with {len(obj)} items, first item type: {type(obj[0]).__name__}")
                    if len(obj) > 0 and isinstance(obj[0], (dict, list)):
                        log_structure(obj[0], prefix + '  ')

            logger.debug("Structure of Kast response:")
            log_structure(response)

            # Handle different response formats
            prediction = {}

            # Try multiple paths to find the prediction data
            # Path 1: Standard document response
            document = response.get('document', {})
            if 'inference' in document:
                inference = document.get('inference', {})
                prediction = inference.get('prediction', {})
                logger.info("Found prediction in standard document response")

            # Path 2: Job response with document
            elif 'api_request' in response and 'document' in response:
                doc = response.get('document', {})
                if 'inference' in doc:
                    inference = doc.get('inference', {})
                    prediction = inference.get('prediction', {})
                    logger.info("Found prediction in job response document")

            # Path 3: Job response with result
            elif 'job' in response and 'result' in response.get('job', {}):
                result = response.get('job', {}).get('result', {})
                if 'document' in result:
                    doc = result.get('document', {})
                    if 'inference' in doc:
                        inference = doc.get('inference', {})
                        prediction = inference.get('prediction', {})
                        logger.info("Found prediction in job result document")

            # Path 4: Direct prediction in response
            elif 'prediction' in response:
                prediction = response.get('prediction', {})
                logger.info("Found prediction directly in response")

            # Path 5: Look for fields directly in the response
            else:
                logger.info("Searching for prediction data directly in response")
                for key in ['invoice_number', 'date', 'due_date', 'total_amount', 'line_items']:
                    if key in response:
                        prediction[key] = response[key]
                if prediction:
                    logger.info("Found prediction fields directly in response")

            # If we still don't have prediction data, log an error
            if not prediction:
                logger.error("Could not find prediction data in any expected location in the response")
                # Log the keys at the top level to help debug
                logger.error(f"Top-level keys in response: {list(response.keys())}")
                if 'document' in response:
                    logger.error(f"Keys in document: {list(response['document'].keys())}")
                return {
                    "success": False,
                    "error": "Could not find prediction data in response",
                    "items": []
                }

            logger.info(f"Full Kast prediction data: {json.dumps(prediction, indent=2)}")
            logger.debug(f"Parsing Kast prediction data: {json.dumps(prediction, indent=2)[:500]}...")

            # Log all available fields in prediction for debugging
            logger.info(f"Available fields in Kast prediction: {list(prediction.keys())}")

            # Extract invoice details - try multiple field names
            invoice_number = self._get_field_value(prediction, 'invoice_number')
            if not invoice_number:
                # Try alternative field names
                alt_invoice_fields = ['invoice_id', 'document_number', 'reference_number', 'number']
                for field in alt_invoice_fields:
                    invoice_number = self._get_field_value(prediction, field)
                    if invoice_number:
                        logger.info(f"Found invoice number in field: {field}")
                        break

            # For Kast, try specific field names first
            date = self._get_field_value(prediction, 'Invoice Date')  # Try Kast-specific field first
            if not date:
                date = self._get_field_value(prediction, 'date')
            if not date:
                # Try alternative field names
                alt_date_fields = ['invoice_date', 'document_date', 'issue_date', 'created_date', 'Date']
                for field in alt_date_fields:
                    date = self._get_field_value(prediction, field)
                    if date:
                        logger.info(f"Found date in field: {field}")
                        break

            due_date = self._get_field_value(prediction, 'due_date')

            # For Kast, the total amount might be called differently
            total_amount = self._get_field_value(prediction, 'Total Amount')  # Try Kast-specific field first
            if not total_amount:
                total_amount = self._get_field_value(prediction, 'Invoice Total')
            if not total_amount:
                total_amount = self._get_field_value(prediction, 'total_amount')
            if not total_amount:
                # Try alternative field names
                alt_total_fields = ['total', 'grand_total', 'amount_total', 'invoice_total', 'net_amount', 'Total']
                for field in alt_total_fields:
                    total_amount = self._get_field_value(prediction, field)
                    if total_amount:
                        logger.info(f"Found total amount in field: {field}")
                        break

            # Set supplier name to Kast since we know the vendor
            supplier_name = "Kast"
            supplier_address = self._get_field_value(prediction, 'supplier_address')

            # Log extracted fields
            logger.info(f"Extracted Kast invoice details: Number={invoice_number}, Date={date}, Total={total_amount}")

            # Extract line items
            line_items = []
            if 'line_items' in prediction:
                logger.info(f"Found {len(prediction['line_items'])} line items in Kast response")
                for item in prediction['line_items']:
                    # Extract line item fields with improved description handling
                    # Updated field names based on new model structure
                    description = self._get_field_value(item, 'Description')  # Primary field for both Bova and Kast

                    # If description is empty, try multiple alternative fields including the typo
                    if not description:
                        description = self._get_field_value(item, 'description')  # Fallback to old names
                    if not description:
                        # Try alternative field names that might contain description
                        alt_fields = ['descripiton', 'product_description', 'item_description', 'name', 'product_name', 'item_name']
                        for field in alt_fields:
                            description = self._get_field_value(item, field)
                            if description:
                                break

                    # If still no description, try to use product_code or other identifiers
                    if not description:
                        product_code = self._get_field_value(item, 'product_code')
                        item_code = self._get_field_value(item, 'item_code')
                        sku = self._get_field_value(item, 'sku')

                        if product_code:
                            description = f"Product {product_code}"
                        elif item_code:
                            description = f"Item {item_code}"
                        elif sku:
                            description = f"SKU {sku}"
                        else:
                            # Log this case for debugging
                            logger.warning(f"Kast line item {len(line_items) + 1} has no description or identifiers. Item data: {item}")
                            # Skip items without any description rather than creating generic names
                            continue

                    # Try multiple field names for quantity (Kast-specific)
                    # Updated field names based on new model structure
                    quantity = self._get_field_value(item, 'Shipped')  # Primary field for Kast
                    if not quantity:
                        quantity = self._get_field_value(item, 'shipped')  # Fallback to old names
                        if quantity:
                            logger.debug(f"Kast: Found quantity in shipped: {quantity}")
                    if not quantity:
                        quantity = self._get_field_value(item, 'quantity')
                        if quantity:
                            logger.debug(f"Kast: Found quantity in quantity: {quantity}")
                    if not quantity:
                        quantity = self._get_field_value(item, 'ordered')
                        if quantity:
                            logger.debug(f"Kast: Found quantity in ordered: {quantity}")
                    if not quantity:
                        quantity = self._get_field_value(item, 'quantity_ordered')
                        if quantity:
                            logger.debug(f"Kast: Found quantity in quantity_ordered: {quantity}")

                    # Try multiple field names for unit_price
                    # Updated field names based on new model structure
                    unit_price = self._get_field_value(item, 'Unit Price')  # Primary field for Kast
                    if not unit_price:
                        unit_price = self._get_field_value(item, 'unit_price')  # Fallback to old names
                    if not unit_price:
                        unit_price = self._get_field_value(item, 'selling_price')

                    # Try multiple field names for amount
                    # Updated field names based on new model structure
                    amount = self._get_field_value(item, 'Extended')  # Primary field for Kast
                    if not amount:
                        amount = self._get_field_value(item, 'extended')  # Fallback to old names
                    if not amount:
                        amount = self._get_field_value(item, 'extensions')
                    if not amount:
                        amount = self._get_field_value(item, 'amount')

                    # Convert amount to float first to check if it's valid
                    try:
                        amount_float = float(amount) if amount else 0
                    except (ValueError, TypeError):
                        amount_float = 0

                    # If still no quantity, default to 1 for line items that have amounts
                    if not quantity and amount_float > 0:
                        quantity = 1
                        logger.debug(f"Defaulting quantity to 1 for item with amount: {amount_float}")

                    # Log available fields in item for debugging
                    if not quantity:
                        logger.debug(f"No quantity found. Available fields in item: {list(item.keys())}")

                    # Convert to appropriate types
                    try:
                        quantity = float(quantity) if quantity else 0
                    except (ValueError, TypeError):
                        quantity = 0

                    try:
                        unit_price = float(unit_price) if unit_price else 0
                    except (ValueError, TypeError):
                        unit_price = 0

                    # Use the already calculated amount_float
                    amount = amount_float

                    line_item = {
                        'description': description,
                        'quantity': quantity,
                        'unit_price': unit_price,
                        'amount': amount
                    }
                    line_items.append(line_item)
                    logger.debug(f"Added line item: {description}, qty: {quantity}, price: {unit_price}, amount: {amount}")

            # Convert total_amount to float
            try:
                total_amount = float(total_amount) if total_amount else 0
            except (ValueError, TypeError):
                total_amount = 0

            # If no total amount found, calculate from line items
            if total_amount == 0 and line_items:
                calculated_total = sum(item.get('amount', 0) for item in line_items)
                if calculated_total > 0:
                    total_amount = calculated_total
                    logger.info(f"Calculated total from line items: {total_amount}")

            result = {
                "success": True,
                "invoice_number": invoice_number,
                "date": date,
                "due_date": due_date,
                "total_amount": total_amount,
                "supplier_name": supplier_name,
                "supplier_address": supplier_address,
                "items": line_items
            }

            logger.info(f"Successfully parsed Kast response with {len(line_items)} line items")
            return result

        except Exception as e:
            logger.error(f"Error parsing Kast response: {str(e)}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            return {
                "success": False,
                "error": f"Error parsing Kast response: {str(e)}",
                "items": []
            }

    def _parse_dekalb_response(self, response):
        """
        Parse response from Dekalb Produce custom API.

        Args:
            response: JSON response from Mindee API

        Returns:
            dict: Structured data in our application's format
        """
        try:
            # Log the full response for debugging
            logger.info(f"Received Dekalb response: {json.dumps(response, indent=2)[:1000]}...")

            # Log the structure of the response to help with debugging
            def log_structure(obj, prefix=''):
                if isinstance(obj, dict):
                    for k, v in obj.items():
                        logger.debug(f"{prefix}Key: {k} (Type: {type(v).__name__})")
                        if isinstance(v, (dict, list)) and k != 'pages':  # Skip pages to avoid too much output
                            log_structure(v, prefix + '  ')
                elif isinstance(obj, list) and len(obj) > 0:
                    logger.debug(f"{prefix}List with {len(obj)} items, first item type: {type(obj[0]).__name__}")
                    if len(obj) > 0 and isinstance(obj[0], (dict, list)):
                        log_structure(obj[0], prefix + '  ')

            logger.debug("Structure of Dekalb response:")
            log_structure(response)

            # Handle different response formats
            prediction = {}

            # Try multiple paths to find the prediction data
            # Path 1: Standard document response
            document = response.get('document', {})
            if 'inference' in document:
                inference = document.get('inference', {})
                prediction = inference.get('prediction', {})
                logger.info("Found prediction in standard document response")

            # Path 2: Job response with document
            elif 'api_request' in response and 'document' in response:
                doc = response.get('document', {})
                if 'inference' in doc:
                    inference = doc.get('inference', {})
                    prediction = inference.get('prediction', {})
                    logger.info("Found prediction in job response document")

            # Path 3: Job response with result
            elif 'job' in response and 'result' in response.get('job', {}):
                result = response.get('job', {}).get('result', {})
                if 'document' in result:
                    doc = result.get('document', {})
                    if 'inference' in doc:
                        inference = doc.get('inference', {})
                        prediction = inference.get('prediction', {})
                        logger.info("Found prediction in job result document")

            # Path 4: Direct prediction in response
            elif 'prediction' in response:
                prediction = response.get('prediction', {})
                logger.info("Found prediction directly in response")

            # Path 5: Look for fields directly in the response
            else:
                logger.info("Searching for prediction data directly in response")
                for key in ['invoice_number', 'date', 'due_date', 'total_amount', 'line_items']:
                    if key in response:
                        prediction[key] = response[key]
                if prediction:
                    logger.info("Found prediction fields directly in response")

            # If we still don't have prediction data, log an error
            if not prediction:
                logger.error("Could not find prediction data in any expected location in the response")
                # Log the keys at the top level to help debug
                logger.error(f"Top-level keys in response: {list(response.keys())}")
                if 'document' in response:
                    logger.error(f"Keys in document: {list(response['document'].keys())}")
                return {
                    "success": False,
                    "error": "Could not find prediction data in response",
                    "items": []
                }

            logger.debug(f"Parsing Dekalb prediction data: {json.dumps(prediction, indent=2)[:500]}...")

            # Extract invoice details - field names should match your custom API
            invoice_number = self._get_field_value(prediction, 'invoice_number')
            date = self._get_field_value(prediction, 'date')
            due_date = self._get_field_value(prediction, 'due_date')
            total_amount = self._get_field_value(prediction, 'total_amount')

            # Set supplier name to Dekalb Produce since we know the vendor
            supplier_name = "Dekalb Produce"
            supplier_address = self._get_field_value(prediction, 'supplier_address')

            # Log extracted fields
            logger.info(f"Extracted Dekalb invoice details: Number={invoice_number}, Date={date}, Total={total_amount}")

            # Extract line items
            line_items = []
            if 'line_items' in prediction:
                logger.info(f"Found {len(prediction['line_items'])} line items in Dekalb response")
                for item in prediction['line_items']:
                    # Extract line item fields with improved description handling
                    description = self._get_field_value(item, 'description')

                    # If description is empty, try multiple alternative fields including the typo
                    if not description:
                        # Try alternative field names that might contain description
                        alt_fields = ['descripiton', 'product_description', 'item_description', 'name', 'product_name', 'item_name']
                        for field in alt_fields:
                            description = self._get_field_value(item, field)
                            if description:
                                break

                    # If still no description, try to use product_code or other identifiers
                    if not description:
                        product_code = self._get_field_value(item, 'product_code')
                        item_code = self._get_field_value(item, 'item_code')
                        sku = self._get_field_value(item, 'sku')

                        if product_code:
                            description = f"Product {product_code}"
                        elif item_code:
                            description = f"Item {item_code}"
                        elif sku:
                            description = f"SKU {sku}"
                        else:
                            # Log this case for debugging
                            logger.warning(f"DeKalb line item {len(line_items) + 1} has no description or identifiers. Item data: {item}")
                            # Skip items without any description rather than creating generic names
                            continue

                    # Try multiple field names for quantity (Dekalb-specific)
                    quantity = self._get_field_value(item, 'quantity')
                    if not quantity:
                        quantity = self._get_field_value(item, 'ordered')
                        if quantity:
                            logger.debug(f"Dekalb: Found quantity in ordered: {quantity}")
                    if not quantity:
                        quantity = self._get_field_value(item, 'quantity_ordered')
                        if quantity:
                            logger.debug(f"Dekalb: Found quantity in quantity_ordered: {quantity}")
                    if not quantity:
                        quantity = self._get_field_value(item, 'quantity_shipped')
                        if quantity:
                            logger.debug(f"Dekalb: Found quantity in quantity_shipped: {quantity}")
                    if not quantity:
                        quantity = self._get_field_value(item, 'shipped')
                        if quantity:
                            logger.debug(f"Dekalb: Found quantity in shipped: {quantity}")

                    # Try multiple field names for unit_price
                    unit_price = self._get_field_value(item, 'unit_price')
                    if not unit_price:
                        unit_price = self._get_field_value(item, 'selling_price')

                    # Try multiple field names for amount
                    amount = self._get_field_value(item, 'amount')
                    if not amount:
                        amount = self._get_field_value(item, 'extensions')
                    if not amount:
                        amount = self._get_field_value(item, 'extended')

                    # Convert amount to float first to check if it's valid
                    try:
                        amount_float = float(amount) if amount else 0
                    except (ValueError, TypeError):
                        amount_float = 0

                    # If still no quantity, default to 1 for line items that have amounts
                    if not quantity and amount_float > 0:
                        quantity = 1
                        logger.debug(f"Defaulting quantity to 1 for item with amount: {amount_float}")

                    # Log available fields in item for debugging
                    if not quantity:
                        logger.debug(f"No quantity found. Available fields in item: {list(item.keys())}")

                    # Convert to appropriate types
                    try:
                        quantity = float(quantity) if quantity else 0
                    except (ValueError, TypeError):
                        quantity = 0

                    try:
                        unit_price = float(unit_price) if unit_price else 0
                    except (ValueError, TypeError):
                        unit_price = 0

                    # Use the already calculated amount_float
                    amount = amount_float

                    line_item = {
                        'description': description,
                        'quantity': quantity,
                        'unit_price': unit_price,
                        'amount': amount
                    }
                    line_items.append(line_item)
                    logger.debug(f"Added line item: {description}, qty: {quantity}, price: {unit_price}, amount: {amount}")

            # Convert total_amount to float
            try:
                total_amount = float(total_amount) if total_amount else 0
            except (ValueError, TypeError):
                total_amount = 0

            # If no total amount found, calculate from line items
            if total_amount == 0 and line_items:
                calculated_total = sum(item.get('amount', 0) for item in line_items)
                if calculated_total > 0:
                    total_amount = calculated_total
                    logger.info(f"Calculated total from line items: {total_amount}")

            result = {
                "success": True,
                "invoice_number": invoice_number,
                "date": date,
                "due_date": due_date,
                "total_amount": total_amount,
                "supplier_name": supplier_name,
                "supplier_address": supplier_address,
                "items": line_items
            }

            logger.info(f"Successfully parsed Dekalb response with {len(line_items)} line items")
            return result

        except Exception as e:
            logger.error(f"Error parsing Dekalb Produce response: {str(e)}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            return {
                "success": False,
                "error": f"Error parsing Dekalb Produce response: {str(e)}",
                "items": []
            }

    def _mock_response(self):
        """
        Generate mock data for development without API key.

        Returns:
            dict: Mock structured data
        """
        logger.info("Generating mock Mindee response for development")
        return {
            "success": True,
            "invoice_number": "INV-12345",
            "date": "2025-05-01",
            "due_date": "2025-05-31",
            "total_amount": 1885.65,
            "supplier_name": "STX FOODSERVICE DISTRIBUTOR",
            "supplier_address": "123 Main St, Anytown, USA",
            "items": [
                {
                    "description": "Chicken Tenders Large Clipped Fresh CVP",
                    "quantity": 1,
                    "unit_price": 549.06,
                    "amount": 549.06
                },
                {
                    "description": "Chicken Wings Fresh Jumbo Party CVP",
                    "quantity": 1,
                    "unit_price": 160.0,
                    "amount": 160.0
                },
                {
                    "description": "Beef Chuck Ground 80/20 Fresh",
                    "quantity": 2,
                    "unit_price": 535.4,
                    "amount": 1070.8
                },
                {
                    "description": "Lettuce Romaine Chopped Fresh",
                    "quantity": 3,
                    "unit_price": 41.7,
                    "amount": 125.1
                }
            ]
        }
